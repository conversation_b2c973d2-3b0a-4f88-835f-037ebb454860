import { gql } from 'apollo-angular';

// Fragment for role fields to reuse across queries
const ROLE_FIELDS = gql`
  fragment RoleFields on Role {
    uuid
    name
    displayName
    createdAt
    updatedAt
  }
`;

// Fragment for permission fields
const PERMISSION_FIELDS = gql`
  fragment PermissionFields on PermissionResponseDto {
    uuid
    name
    description
    groupName
  }
`;

// Queries
export const LIST_ROLES = gql`
  query ListRoles {
    listRoles {
      status
      code
      errorDescription
      dataList {
        ...RoleFields
      }
    }
  }
  ${ROLE_FIELDS}
`;

export const LIST_PERMISSIONS = gql`
  query ListPermissions {
    listPermissions {
      status
      code
      errorDescription
      dataList {
        ...PermissionFields
      }
    }
  }
  ${PERMISSION_FIELDS}
`;

// Mutations
export const SAVE_ROLE = gql`
  mutation SaveRole($roleDto: RoleDtoInput!) {
    saveRole(roleDto: $roleDto) {
      status
      code
      errorDescription
      data {
        ...RoleFields
      }
    }
  }
  ${ROLE_FIELDS}
`;
